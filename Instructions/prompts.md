So I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks but real methods/components. the following tests to implement will be - 4.4 ResultProcessor Tests
	•	Test Result Processing: Test processing task results
	•	Test Result Transformation: Test applying transformations to results
	•	Test Result Caching: Test caching results for reuse
	•	Test Result History: Test retrieving results from history
	
	




