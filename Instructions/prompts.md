So I have the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor module and I would you to analyze it and implement tests in the - /Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration/executor_tests.rs
/Users/<USER>/Documents/prisma_workspace/prisma_ai/tests/integration_tests.rs ( entry point) files. the test will NOT use mocks but real methods/components. the following tests to implement will be - 4.3 MemoryManager Tests
	•	Test Memory Allocation: Test allocating memory for a task
	•	Test Memory Storage: Test storing values in memory
	•	Test Memory Retrieval: Test retrieving values from memory
	•	Test Memory Eviction: Test memory eviction strategies
	•	Test Memory Cleanup: Test cleaning up memory for a task
	•	Test LTM Integration: Test long-term memory integration if enabled
	
	




